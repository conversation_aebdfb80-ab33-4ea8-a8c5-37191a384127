"""
ComfyUI Agent请求运行器
专注于工作流执行，路由决策委托给 unified_routing_system
"""

from __future__ import annotations

import asyncio
import typing
from typing import List, Optional, Dict, Any, TYPE_CHECKING
import time

# 使用TYPE_CHECKING避免循环导入
if TYPE_CHECKING:
    from ...core import app

from .. import runner
from ...core import entities as core_entities
from .. import entities as llm_entities
from ...core.session.models import WorkflowType
from ...core.session.states import is_execution_command, is_cancel_command

# 导入各个处理器（已迁移到shared目录）
from ...workers.shared.image_handlers.standard_image_handler import StandardImageHandler
from ...workers.shared.image_handlers.kontext_image_handler import KontextImageHandler
from ...workers.shared.sync.admin_sync_handler import AdminSyncHandler

# 只导入路由系统，不继承复杂的 Mixin
from ...core.workflow.unified_routing_system import get_unified_router

# 导入统一消息发送器
from ...core.message.sender import message_sender


@runner.runner_class('comfyui-agent')
class ComfyUIAgentRunner(runner.RequestRunner):
    """ComfyUI Agent请求运行器
    
    专注于工作流执行，路由决策完全委托给 unified_routing_system：
    - 会话管理和状态控制
    - 工作流执行和结果处理
    - 用户交互和反馈
    
    不包含路由逻辑 - 路由由 smart_hybrid_agent 和 unified_routing_system 负责
    """

    def __init__(self, ap: 'app.Application', pipeline_config: dict):
        super().__init__(ap, pipeline_config)
        
        # 初始化处理器
        self._init_handlers()
        
        # 会话管理器
        from ...core.session.manager import SessionManager
        self.session_manager = SessionManager(cleanup_interval=300)
        
        # 缓存配置和处理器
        self.pipeline_config = pipeline_config
        self.workflow_manager = None
        self.standard_handler = None
        self.kontext_handler = None
        self.admin_sync_handler = None
        
        # 调试计数器
        self.debug_counter = 0
        self._handlers_initialized = False
        
        # 统一会话管理器
        if self.ap and self.ap.logger:
            self.session_manager.set_logger(self.ap.logger)
        
        # 🔥 重要：只获取路由器引用，不继承路由逻辑
        self.unified_router = get_unified_router(ap)
        
        # 减少冗余日志 - 只在首次初始化时记录
        if not hasattr(ComfyUIAgentRunner, '_first_init_logged'):
            self.ap.logger.info("✅ ComfyUIAgent 初始化完成 - 专注于工作流执行")
            ComfyUIAgentRunner._first_init_logged = True

    async def run(self, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """执行工作流 - 不包含路由逻辑"""
        try:
            self.debug_counter += 1
            # 减少冗余日志 - 只记录关键执行信息
            user_text = self._extract_user_text(query)
            self.ap.logger.info(f"🔄 ComfyUIAgent 处理请求: {user_text[:50]}{'...' if len(user_text) > 50 else ''}")
            
            # 1. 基础数据提取
            user_text = self._extract_user_text(query)
            user_images = await self._extract_user_images(query)
            user_id = self._get_user_id(query)
            chat_id = self._get_chat_id(query)

            # 2. 检查是否是 --again 请求
            from ...core.intent.parameter_parser import parameter_parser
            parsed_params = parameter_parser.parse_user_input(user_text)
            if parsed_params.use_again:
                self.ap.logger.info("🔄 检测到 --again 参数，处理重新生成请求")
                # 🔥 修复：调用正确的方法名
                async for message in self._handle_again_request(user_id, chat_id, query):
                    yield message
                return
            
            self.ap.logger.info(f"📝 用户消息: {user_text[:100]}...")
            self.ap.logger.info(f"🖼️ 用户图片: {len(user_images)} 张")
            
            # 2. 获取现有会话
            session = self.session_manager.get_session(user_id, chat_id)
            
            # 3. 如果没有会话但被调用，说明路由系统已决策，直接创建会话
            if not session:
                self.ap.logger.info("🆕 检查是否需要创建工作流会话")
                
                # 🔥 重要：这里应该接收路由结果，而不是重新路由
                # 由于当前架构限制，我们暂时进行简单的路由检查
                workflow_type = self._simple_workflow_detection(user_text)
                
                # 如果没有检测到触发词，不创建会话
                if workflow_type is None:
                    self.ap.logger.info("❌ 未检测到触发词，不创建工作流会话")
                    # 不返回任何消息，让消息继续处理
                    return
                
                session = self.session_manager.create_session(
                    user_id=user_id,
                    workflow_type=workflow_type,
                    chat_id=chat_id,
                    prompt="",
                    timeout_minutes=10
                )
                
                if user_text.strip():
                    session.add_message(user_text, "trigger")
                
                # 添加用户图片
                if user_images:
                    for img_data in user_images:
                        img_type = self._detect_image_type(img_data, user_text)
                        session.add_image(img_data, img_type)
                
                self.ap.logger.info(f"✅ 会话创建完成: {session.workflow_type.value}")
                
                # 提供启动响应
                startup_content = f"🎨 **{session.workflow_type.value}工作流已启动**\n" \
                                f"🖼️ **图片**: {len(session.images)} 张\n" \
                                f"🚀 **可发送指令**: 开始 | go | 取消"
                startup_message = message_sender.create_text_message(startup_content)
                yield llm_entities.Message(
                    role=startup_message['role'],
                    content=startup_message['content']
                )
                return
            
            # 4. 处理有活跃会话的情况
            self.ap.logger.info(f"🔄 处理活跃会话: {session.session_id}")
            
            # 初始化处理器
            self._initialize_handlers(query)
            
            # 检查是否是执行指令
            if is_execution_command(user_text):
                self.ap.logger.info("🚀 检测到执行指令")
                
                # 根据工作流类型执行不同的处理逻辑
                if session.workflow_type == WorkflowType.AIGEN:
                    async for message in self._execute_aigen_workflow(session, query):
                        yield message
                elif session.workflow_type == WorkflowType.KONTEXT:
                    async for message in self._execute_kontext_workflow(session, query):
                        yield message
                elif session.workflow_type == WorkflowType.KONTEXT_API:
                    async for message in self._execute_kontext_api_workflow(session, query):
                        yield message
                else:
                    error_content = f"❌ 未知工作流类型: {session.workflow_type.value}"
                    error_message = message_sender.create_text_message(error_content)
                    yield llm_entities.Message(
                        role=error_message['role'],
                        content=error_message['content']
                    )
                return
            
            # 检查是否是取消指令
            if is_cancel_command(user_text):
                self.ap.logger.info("❌ 检测到取消指令")
                self._cleanup_session_completely(user_id, chat_id, "用户取消")
                cancel_content = "❌ 工作流已取消"
                cancel_message = message_sender.create_text_message(cancel_content)
                yield llm_entities.Message(
                    role=cancel_message['role'],
                    content=cancel_message['content']
                )
                return
            
            # 其他情况，更新会话内容
            if user_images:
                self.ap.logger.info(f"🔍 [DEBUG] 准备添加 {len(user_images)} 张图片到会话")
                added_count = 0
                for i, img_data in enumerate(user_images):
                    context_text = session.get_all_messages_text() if session else ""
                    img_type = self._detect_image_type(img_data, context_text)
                    self.ap.logger.info(f"🔍 [DEBUG] 图片 {i+1}: 大小={len(img_data)} bytes, 类型={img_type}")

                    success = session.add_image(img_data, img_type)
                    if success:
                        added_count += 1
                        self.ap.logger.info(f"✅ [DEBUG] 图片 {i+1} 添加成功")
                    else:
                        self.ap.logger.warning(f"❌ [DEBUG] 图片 {i+1} 添加失败（可能重复或超限）")

                self.ap.logger.info(f"📷 成功添加了 {added_count}/{len(user_images)} 张图片到会话，当前总数: {len(session.images)}")

                image_added_content = f"✅ **图片已添加**\n\n" \
                                    f"🖼️ **当前图片总数**: {len(session.images)}\n" \
                                    f"📝 **提示**: 可以继续添加图片或发送 '开始' 执行工作流"
                image_added_message = message_sender.create_text_message(image_added_content)
                yield llm_entities.Message(
                    role=image_added_message['role'],
                    content=image_added_message['content']
                )
                return
            
            # 处理文本输入
            if user_text.strip():
                session.add_message(user_text, "user_input")

                text_added_content = f"✅ **内容已添加**\n\n" \
                                   f"📝 **当前内容**: {user_text[:100]}{'...' if len(user_text) > 100 else ''}\n" \
                                   f"🚀 **可发送指令**: 开始 | go | 取消"
                text_added_message = message_sender.create_text_message(text_added_content)
                yield llm_entities.Message(
                    role=text_added_message['role'],
                    content=text_added_message['content']
                )
                return
                
        except Exception as e:
            self.ap.logger.error(f"❌ ComfyUIAgent 执行错误: {str(e)}")
            error_content = f"❌ 处理请求时出错: {str(e)}"
            error_message = message_sender.create_text_message(error_content)
            yield llm_entities.Message(
                role=error_message['role'],
                content=error_message['content']
            )

    async def _handle_again_request(self, user_id: str, chat_id: str, query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """
        处理 --again 重新生成请求

        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            query: 查询对象
        """
        try:
            self.ap.logger.info(f"🔄 开始处理 --again 请求 - 用户: {user_id}")

            # 发送开始消息
            start_content = "🔄 **正在重新生成上一次的图片...**"
            start_message = message_sender.create_text_message(start_content)
            yield llm_entities.Message(
                role=start_message['role'],
                content=start_message['content']
            )

            # 🔥 使用统一Again管理器处理所有工作流类型
            from ...workers.shared.again_manager import unified_again_manager, AgainRequest

            # 创建Again请求对象
            again_request = AgainRequest(
                user_id=user_id,
                chat_id=chat_id,
                workflow_type="aigen"  # ComfyUIAgent默认处理aigen类型
            )

            result = await unified_again_manager.handle_again_request(again_request)

            if result.success:
                # 生成成功
                self.ap.logger.info("✅ --again 请求执行成功")

                # 发送图片
                if result.image_data:
                    # 确保处理器已经初始化
                    if self.standard_handler is None:
                        self._initialize_handlers(query)

                    # 使用标准图片处理器发送图片
                    if self.standard_handler is not None:
                        send_success = await self.standard_handler.send_image_to_wechat(
                            result.image_data, query
                        )
                        if not send_success:
                            self.ap.logger.error("❌ --again 图片发送失败")
                    else:
                        self.ap.logger.error("❌ --again 图片处理器未初始化")

                # 发送完成消息
                metadata = result.metadata or {}
                completion_content = self._format_completion_message(
                    workflow_type=metadata.get('workflow_type', 'unknown'),
                    execution_time=0,  # --again 请求不计算执行时间
                    lora_info=metadata.get('lora_info', {}),
                    is_again=True,
                    original_prompt=metadata.get('user_prompt', '')
                )
                completion_message = message_sender.create_text_message(completion_content)
                yield llm_entities.Message(
                    role=completion_message['role'],
                    content=completion_message['content']
                )
            else:
                # 生成失败
                error_content = f"❌ **重新生成失败**\n\n{result.error_message}"
                error_message = message_sender.create_text_message(error_content)
                yield llm_entities.Message(
                    role=error_message['role'],
                    content=error_message['content']
                )

        except Exception as e:
            self.ap.logger.error(f"❌ 处理 --again 请求异常: {e}")
            error_content = f"❌ **重新生成时出错**: {str(e)}"
            error_message = message_sender.create_text_message(error_content)
            yield llm_entities.Message(
                role=error_message['role'],
                content=error_message['content']
            )

    def _format_completion_message(self, workflow_type: str, execution_time: float, lora_info: dict, is_again: bool = False, original_prompt: str = "") -> str:
        """格式化完成消息"""
        if is_again:
            content = f"✅ **重新生成完成**\n\n"
            content += f"🔄 **基于上一次**: {original_prompt[:50]}{'...' if len(original_prompt) > 50 else ''}\n"
        else:
            content = f"✅ **图片生成完成**\n\n"

        content += f"⚙️ **工作流**: {workflow_type}\n"

        # LoRA信息
        loras_used = lora_info.get('loras_used', [])
        if loras_used:
            lora_names = [name[:12] + '...' if len(name) > 12 else name for name in loras_used[:3]]
            content += f"🎨 **LoRA**: {', '.join(lora_names)}\n"
        else:
            content += f"🎨 **LoRA**: no lora\n"

        if not is_again:
            content += f"⏱️ **耗时**: {execution_time:.1f}秒"

        return content

    def _simple_workflow_detection(self, user_text: str) -> Optional[WorkflowType]:
        """简单的工作流检测 - 仅用于兼容性，应该被路由系统替代"""
        if not user_text:
            return None  # 没有文本，不创建工作流
            
        user_text_lower = user_text.lower().strip()
        
        # 检查触发词（必须以触发词开头，后跟空格）
        if user_text_lower.startswith("kontext_api "):
            return WorkflowType.KONTEXT_API
        elif user_text_lower.startswith("kontext "):
            return WorkflowType.KONTEXT
        elif user_text_lower.startswith("aigen "):
            return WorkflowType.AIGEN
        else:
            return None  # 没有触发词，不创建工作流

    def _init_handlers(self):
        """初始化处理器（延迟加载）"""
        # 不在初始化时立即创建处理器，而是在需要时才创建
        pass

    def _initialize_handlers(self, query: core_entities.Query):
        """初始化处理器（需要query对象）"""
        if self._handlers_initialized:
            return
            
        try:
            try:
                comfyui_config = self.pipeline_config.get('ai', {}).get('comfyui-agent', {})
                self.workflow_path = comfyui_config.get('workflow-path', 'workflows')
                self.comfyui_enabled = comfyui_config.get('enabled', True)
                self.kontext_enabled = comfyui_config.get('kontext-enabled', True)
                # 减少冗余日志 - 只在首次初始化时记录配置
                if not hasattr(ComfyUIAgentRunner, '_config_logged'):
                    self.ap.logger.info(f"ComfyUI Agent配置: enabled={self.comfyui_enabled}, kontext-enabled={self.kontext_enabled}")
                    ComfyUIAgentRunner._config_logged = True
            except Exception as e:
                self.ap.logger.error(f"ComfyUI配置初始化失败: {e}")
                self.workflow_path = 'workflows'
                self.comfyui_enabled = True
                self.kontext_enabled = True

            if self.standard_handler is None:
                self.standard_handler = StandardImageHandler(self.ap, self.pipeline_config)
                # 减少冗余日志 - 只在首次初始化时记录
                if not hasattr(ComfyUIAgentRunner, '_handlers_logged'):
                    self.ap.logger.info("图像处理器初始化成功")

            if self.kontext_handler is None:
                self.kontext_handler = KontextImageHandler(self.ap, self.pipeline_config)

            if self.admin_sync_handler is None:
                self.admin_sync_handler = AdminSyncHandler(self.ap, self.pipeline_config, query)
            
            if self.workflow_manager is None:
                try:
                    from pkg.workers.flux.flux_workflow_manager import FluxWorkflowManager
                    # 减少冗余日志 - 只在首次初始化时记录详细信息
                    if not hasattr(ComfyUIAgentRunner, '_workflow_manager_logged'):
                        self.ap.logger.info(f"检查ap对象属性:")
                        self.ap.logger.info(f"  - ap类型: {type(self.ap)}")
                        self.ap.logger.info(f"  - ap.logger: {self.ap.logger is not None}")
                        self.ap.logger.info(f"  - ap.model_mgr: {self.ap.model_mgr is not None}")
                        self.ap.logger.info(f"  - ap.pipeline_config: {hasattr(self.ap, 'pipeline_config')}")
                        self.ap.logger.info(f"  - self.pipeline_config类型: {type(self.pipeline_config)}")
                        ComfyUIAgentRunner._workflow_manager_logged = True

                    self.workflow_manager = FluxWorkflowManager(self.ap, self.pipeline_config)

                    # 减少冗余日志 - 只在首次初始化时记录
                    if not hasattr(ComfyUIAgentRunner, '_handlers_logged'):
                        self.ap.logger.info("FluxWorkflowManager 初始化成功")
                        self.ap.logger.info(f"FluxWorkflowManager属性检查:")
                        self.ap.logger.info(f"  - seed_manager: {self.workflow_manager.seed_manager is not None}")
                        self.ap.logger.info(f"  - node_mapper: {self.workflow_manager.node_mapper is not None}")
                        self.ap.logger.info(f"  - lora_integration: {self.workflow_manager.lora_integration is not None}")
                        ComfyUIAgentRunner._handlers_logged = True
                except Exception as e:
                    self.ap.logger.error(f"FluxWorkflowManager 初始化失败: {e}")
                    import traceback
                    self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
                    self.workflow_manager = None
            
            self._handlers_initialized = True
                    
        except Exception as e:
            self.ap.logger.error(f"处理器初始化失败: {e}")
            self.ap.logger.exception("处理器初始化异常详情：")

    def _extract_user_text(self, query: core_entities.Query) -> str:
        """提取用户文本"""
        user_text = ""
        if query.user_message and query.user_message.content:
            if isinstance(query.user_message.content, str):
                user_text = query.user_message.content
            elif isinstance(query.user_message.content, list):
                for content in query.user_message.content:
                    if hasattr(content, 'text') and content.text and hasattr(content, 'type') and content.type == 'text':
                        user_text += str(content.text)
        return user_text.strip()

    async def _extract_user_images(self, query: core_entities.Query) -> List[bytes]:
        """提取用户消息中的图片数据 - 使用统一的ImageProcessor"""
        try:
            # 使用统一的图片处理器
            from pkg.core.image.processor import image_processor
            images = await image_processor.extract_user_images(query)
            self.ap.logger.info(f"📷 统一图片处理器提取到 {len(images)} 张图片")
            return images
        except Exception as e:
            self.ap.logger.error(f"提取用户图片失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            return []

    def _get_user_id(self, query: core_entities.Query) -> str:
        """获取用户ID - 与Linux环境文件命名保持一致"""
        try:
            # 🔥 修复：优先使用query.sender_id，这是微信平台直接提供的wxid
            if hasattr(query, 'sender_id') and query.sender_id:
                user_id = str(query.sender_id)
                # 移除调试日志，避免日志污染
                return user_id

            # 回退方案1：从message_event中提取sender.id
            message_event = getattr(query, 'message_event', None)
            if message_event:
                sender = getattr(message_event, 'sender', None)
                if sender and hasattr(sender, 'id') and sender.id:
                    return str(sender.id)

            # 回退方案2：使用BaseAgent的标准逻辑
            return super()._get_user_id(query)

        except Exception as e:
            self.ap.logger.error(f"❌ 获取用户ID失败: {e}")
            return "unknown_user"

    def _get_chat_id(self, query: core_entities.Query) -> str:
        """获取聊天ID"""
        try:
            return f"{query.launcher_type.value}_{query.launcher_id}"
        except Exception as e:
            self.ap.logger.error(f"获取聊天ID失败: {e}")
            return "unknown_chat"

    # 🔥 智能图片类型检测方法（保留，用作统一路由系统的补充）
    def _detect_image_type(self, image_data: bytes, user_prompt: str) -> str:
        """
        智能检测图片类型（控制图 vs 参考图）
        
        Args:
            image_data: 图片二进制数据
            user_prompt: 用户提示词
            
        Returns:
            图片类型: 'control' 或 'reference'
        """
        try:
            # 1. 基于用户提示词检测
            prompt_lower = user_prompt.lower()
            control_keywords = ['控制', '草图', '线稿', '姿势', 'pose', '深度', 'depth', '边缘', 'edge', '轮廓', 'outline']
            reference_keywords = ['参考', '风格', 'style', '类似', '像', '根据', '基于']
            
            # 检查控制图关键词
            if any(keyword in prompt_lower for keyword in control_keywords):
                return 'control'
            
            # 检查参考图关键词
            if any(keyword in prompt_lower for keyword in reference_keywords):
                return 'reference'
            
            # 2. 基于图片特征检测
            try:
                from PIL import Image
                import io
                
                with Image.open(io.BytesIO(image_data)) as img:
                    # 分析图片复杂度（简单的草图通常颜色较少，边缘较多）
                    if img.mode in ['L', 'LA']:  # 灰度图
                        # 灰度图更可能是控制图
                        return 'control'
                    
                    # 检查图片大小和格式
                    if len(image_data) < 100 * 1024:  # 小于100KB
                        # 小图片更可能是控制图
                        return 'control'
                    
                    # 检查是否是PNG格式（通常用于线稿）
                    if img.format == 'PNG':
                        # 分析颜色数量
                        colors = img.getcolors(maxcolors=256)
                        if colors and len(colors) < 50:  # 颜色较少
                            return 'control'
                    
                    # 默认作为参考图
                    return 'reference'
                    
            except Exception as e:
                self.ap.logger.warning(f"图片特征分析失败: {e}")
                # 分析失败时，根据提示词长度判断
                if len(user_prompt.strip()) < 20:
                    return 'control'  # 短提示词更可能是控制图
                else:
                    return 'reference'  # 长提示词更可能是参考图
                    
        except Exception as e:
            self.ap.logger.error(f"图片类型检测失败: {e}")
            return 'reference'  # 出错时默认参考图

    def _is_image_to_text_request(self, user_text: str, session_images: list) -> bool:
        """
        检测是否是图片反推请求

        Args:
            user_text: 用户输入文本
            session_images: 会话中的图片列表

        Returns:
            bool: 是否是图片反推请求
        """
        if not user_text or not session_images:
            return False

        user_text_lower = user_text.lower().strip()

        # 检查图片反推关键词
        image_to_text_keywords = [
            '图片反推', '反推', '图片识别', '识别图片', '图片描述',
            '描述图片', '图片内容', '看图说话', '图转文', '图片转文字',
            'image to text', 'describe image', 'caption'
        ]

        return any(keyword in user_text_lower for keyword in image_to_text_keywords)

    # 🔥 新增：执行aigen工作流的方法
    async def _execute_aigen_workflow(self, session, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """执行AIGEN工作流 - 专注于执行，不包含路由逻辑"""
        try:
            # 获取会话信息
            user_text = session.get_all_messages_text()
            session_images = session.images
            
            self.ap.logger.info(f"🎨 执行AIGEN工作流 - 提示词: {user_text[:50]}...")
            self.ap.logger.info(f"🖼️ 图片数量: {len(session_images)}")
            
            # 🔥 重要：comfyui_agent 不进行路由决策，只执行工作流
            # 路由决策应该由 smart_hybrid_agent 和 unified_routing_system 完成

            # 检查是否有路由结果（由 smart_hybrid_agent 传递）
            routing_result = getattr(query, 'routing_result', None)

            # 🔥 修复：确保工作流真正执行
            self.ap.logger.info("=" * 60)
            self.ap.logger.info("🚀 开始执行AIGEN工作流")
            self.ap.logger.info("=" * 60)
            
            # 🔥 如果没有路由结果，自动调用统一路由系统
            if not routing_result:
                self.ap.logger.info("🔄 没有路由结果，自动调用统一路由系统")
                try:
                    from ...core.workflow.unified_routing_system import get_unified_router
                    unified_router = get_unified_router(self.ap)
                    
                    # 🔥 关键修复：确保query对象有正确的pipeline_config和ap属性
                    if not query.pipeline_config:
                        query.pipeline_config = self.pipeline_config.copy()
                        self.ap.logger.info("✅ [FIX] 已将pipeline_config设置到query对象")
                        self.ap.logger.info(f"🔍 [FIX] pipeline_config: {query.pipeline_config}")
                    else:
                        self.ap.logger.info("✅ [FIX] query对象已有pipeline_config")

                    # 🔥 关键修复：确保query对象有ap属性，供Flux优化器使用（必须在调用统一路由系统之前设置）
                    if not hasattr(query, 'ap') or not query.ap:
                        # 使用setattr安全地设置属性，避免Pydantic验证错误
                        setattr(query, 'ap', self.ap)
                        self.ap.logger.info("✅ [FIX] 已将ap实例设置到query对象")
                    else:
                        self.ap.logger.info("✅ [FIX] query对象已有ap属性")

                    # 🔥 修复：验证ComfyUI Agent的模型配置
                    ai_config = query.pipeline_config.get('ai', {})
                    runner_type = ai_config.get('runner', {}).get('runner', 'local-agent')

                    if runner_type == 'comfyui-agent':
                        model_uuid = ai_config.get('comfyui-agent', {}).get('model', '')
                        config_source = 'comfyui-agent'
                    else:
                        model_uuid = ai_config.get('local-agent', {}).get('model', '')
                        config_source = 'local-agent'

                    if model_uuid:
                        self.ap.logger.info(f"✅ [FIX] 找到{config_source}模型配置: {model_uuid}")
                    else:
                        self.ap.logger.warning(f"❌ [FIX] 未找到{config_source}模型配置")

                    routing_result = await unified_router.route_unified(
                        user_text=user_text,
                        has_images=bool(session_images and len(session_images) > 0),
                        image_count=len(session_images) if session_images else 0,
                        query=query
                    )
                    
                    if routing_result:
                        self.ap.logger.info(f"✅ 统一路由系统返回结果: {routing_result.workflow_subtype.value}")
                    else:
                        self.ap.logger.warning("❌ 统一路由系统返回空结果")
                        
                except Exception as e:
                    self.ap.logger.error(f"❌ 调用统一路由系统失败: {e}")
                    routing_result = None
            
            if routing_result:
                # 使用路由结果中的信息
                self.ap.logger.info(f"📊 使用路由结果:")
                self.ap.logger.info(f"   工作流子类型: {routing_result.workflow_subtype.value}")
                self.ap.logger.info(f"   置信度: {routing_result.confidence.value}")
                self.ap.logger.info(f"   推理: {routing_result.reasoning}")
                self.ap.logger.info(f"   建议提示词: {routing_result.suggested_prompt}")
                self.ap.logger.info(f"   工作流文件: {routing_result.workflow_file}")
                
                # 工作流说明已在routing_result中
                
                # 🔥 修复：用户go指令后的三段式消息（生成前） - 分3条单独消息发送
                if routing_result.suggested_prompt and routing_result.suggested_prompt.strip():
                    optimized_prompt = routing_result.suggested_prompt
                else:
                    optimized_prompt = user_text

                # 🔥 关键：先检查是否为需要用户重新输入的警告消息
                if optimized_prompt and (optimized_prompt.startswith("⚠️ LLM提示词优化暂时不可用") or
                                       optimized_prompt.startswith("⚠️ LLM returned Chinese text") or
                                       optimized_prompt.startswith("⚠️ Unable to extract valid prompt")):
                    # 直接返回提示消息，不继续执行工作流
                    warning_message = message_sender.create_text_message(optimized_prompt)
                    yield llm_entities.Message(
                        role=warning_message['role'],
                        content=warning_message['content']
                    )
                    return

                # 第一条消息：工作流信息（使用session_manager生成）
                execution_msg = self.session_manager.generate_workflow_execution_message(
                    session, routing_result
                )
                execution_message = message_sender.create_text_message(execution_msg)
                yield llm_entities.Message(
                    role=execution_message['role'],
                    content=execution_message['content']
                )

                # 添加延迟避免微信防刷
                await asyncio.sleep(1.0)
                
                # 第二条消息：提示词（使用session_manager生成）
                prompt_msg = self.session_manager.generate_optimized_prompt_message(optimized_prompt)
                prompt_message = message_sender.create_text_message(prompt_msg)
                yield llm_entities.Message(
                    role=prompt_message['role'],
                    content=prompt_message['content']
                )
                
                # 添加延迟避免微信防刷
                await asyncio.sleep(1.0)
                
                # 第三条消息：开始生成状态（使用session_manager生成）
                generating_msg = self.session_manager.generate_generating_status_message()
                generating_message = message_sender.create_text_message(generating_msg)
                yield llm_entities.Message(
                    role=generating_message['role'],
                    content=generating_message['content']
                )
                
                # 🔥 修复：直接使用FluxWorkflowManager执行工作流
                if self.workflow_manager:
                    self.ap.logger.info("🎯 使用FluxWorkflowManager执行工作流")

                    # 🔥 修复：确保工作流真正执行
                    workflow_file = getattr(routing_result, 'workflow_file', None)

                    # 🔥 关键修复：使用WorkflowSession而不是core_entities.Session
                    # session参数是WorkflowSession类型，有forced_workflow_file字段
                    # 但query.session可能是core_entities.Session类型，没有这个字段
                    if workflow_file:
                        # 直接在WorkflowSession上设置（session参数）
                        session.forced_workflow_file = workflow_file
                        self.ap.logger.info(f"✅ 工作流文件已设置到WorkflowSession: {workflow_file}")

                        # 确保query.session指向正确的session对象
                        query.session = session
                        self.ap.logger.info(f"✅ query.session已更新为兼容的WorkflowSession")
                    else:
                        self.ap.logger.warning("⚠️ 未找到工作流文件，将使用默认工作流")

                    # 执行工作流（工作流管理器会检查forced_workflow_file）
                    result = await self.workflow_manager.execute_workflow(
                        prompt=optimized_prompt,
                        query=query,
                        session_images=session_images
                    )

                    self.ap.logger.info(f"🔍 工作流执行结果: success={result.success}, has_image_data={bool(result.image_data)}")
                    if not result.success:
                        self.ap.logger.error(f"❌ 工作流执行失败: {result.error_message}")
                    
                    if result.success and result.image_data:
                        # 确保处理器已经初始化
                        if self.standard_handler is None:
                            self._initialize_handlers(query)
                        
                        # 发送图片 - 添加空检查
                        if self.standard_handler is not None:
                            send_success = await self.standard_handler.send_image_to_wechat(
                                result.image_data, query
                            )
                            if send_success:
                                image_message = await self.standard_handler.create_image_message(
                                    result.image_data
                                )
                                if image_message:
                                    yield image_message
                            else:
                                send_error_msg = self.session_manager.generate_send_error_message("send_failed")
                                send_error_message = message_sender.create_text_message(send_error_msg)
                                yield llm_entities.Message(
                                    role=send_error_message['role'],
                                    content=send_error_message['content']
                                )
                        else:
                            processor_error_msg = self.session_manager.generate_send_error_message("processor_not_initialized")
                            processor_error_message = message_sender.create_text_message(processor_error_msg)
                            yield llm_entities.Message(
                                role=processor_error_message['role'],
                                content=processor_error_message['content']
                            )
                        
                        # 完成消息（使用session_manager生成）
                        metadata = result.metadata or {}
                        execution_time = metadata.get('execution_time', 0)
                        execution_time_rounded = round(execution_time) if execution_time else 0
                        success_msg = self.session_manager.generate_workflow_completion_message(
                            execution_time_rounded, metadata
                        )
                        success_message = message_sender.create_text_message(success_msg)
                        yield llm_entities.Message(
                            role=success_message['role'],
                            content=success_message['content']
                        )

                        # 🔥 重要：工作流执行成功后清理会话
                        self.ap.logger.info("✅ AIGEN工作流执行成功，清理会话")
                        self._cleanup_session_completely(session.user_id, session.chat_id, "成功完成")
                    else:
                        error_msg = self.session_manager.generate_workflow_error_message(
                            result.error_message or '未知错误', "execution"
                        )
                        error_message = message_sender.create_text_message(error_msg)
                        yield llm_entities.Message(
                            role=error_message['role'],
                            content=error_message['content']
                        )

                        # 🔥 重要：工作流执行失败后也要清理会话
                        self.ap.logger.info("❌ AIGEN工作流执行失败，清理会话")
                        self._cleanup_session_completely(session.user_id, session.chat_id, "执行失败")
                else:
                    manager_error_msg = self.session_manager.generate_workflow_error_message(
                        "工作流管理器未初始化，无法执行工作流", "manager"
                    )
                    manager_error_message = message_sender.create_text_message(manager_error_msg)
                    yield llm_entities.Message(
                        role=manager_error_message['role'],
                        content=manager_error_message['content']
                    )
                    
            else:
                self.ap.logger.warning("❌ 没有路由结果，无法执行工作流")
                
                # 🔥 新逻辑：设置会话状态为等待工作流选择
                from pkg.core.session.models import SessionState
                session.state = SessionState.WAITING_FOR_WORKFLOW_SELECTION
                
                # 准备工作流选项
                workflow_options = self._generate_workflow_options(session_images, user_text)
                
                # 询问用户选择工作流
                workflow_selection_content = f"🤖 **无法自动识别最佳工作流**\n\n" \
                                           f"📝 **提示词**: {user_text}\n" \
                                           f"🖼️ **图片**: {len(session_images)} 张\n\n" \
                                           f"💡 **请选择您希望使用的工作流类型**：\n\n" \
                                           f"{workflow_options}\n\n" \
                                           f"💬 **使用方法**: 请回复对应的**数字编号**（如：1）"
                workflow_selection_message = message_sender.create_text_message(workflow_selection_content)
                yield llm_entities.Message(
                    role=workflow_selection_message['role'],
                    content=workflow_selection_message['content']
                )
                
                # 不继续执行工作流，等待用户选择
                return
                
        except Exception as e:
            self.ap.logger.error(f"❌ 执行AIGEN工作流失败: {e}")

            # 🔥 异常时也要清理会话
            try:
                user_id = self._get_user_id(query)
                chat_id = self._get_chat_id(query)
                self._cleanup_session_completely(user_id, chat_id, "异常中断")
            except Exception as cleanup_error:
                self.ap.logger.error(f"❌ 异常清理会话失败: {cleanup_error}")

            error_msg = self.session_manager.generate_workflow_error_message(str(e), "general")
            error_message = message_sender.create_text_message(error_msg)
            yield llm_entities.Message(
                role=error_message['role'],
                content=error_message['content']
            )

    # 🔥 新增：执行kontext工作流的方法
    async def _execute_kontext_workflow(self, session, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """执行kontext工作流"""
        try:
            # 确保处理器已经初始化
            if self.kontext_handler is None:
                self._initialize_handlers(query)

            if self.kontext_handler:
                # 提取必要的参数 - 添加安全的空检查
                user_text = ""
                if hasattr(query, 'message_event') and query.message_event and hasattr(query.message_event, 'message_chain') and query.message_event.message_chain:
                    user_text = query.message_event.message_chain.display if hasattr(query.message_event.message_chain, 'display') else ""
                
                user_images = self.kontext_handler.extract_user_images(query.message_event) if hasattr(query, 'message_event') and query.message_event else []
                
                # 安全获取user_id和chat_id
                user_id = "unknown"
                chat_id = "unknown"
                if hasattr(query, 'message_event') and query.message_event and hasattr(query.message_event, 'sender') and query.message_event.sender and hasattr(query.message_event.sender, 'id'):
                    user_id = str(query.message_event.sender.id)
                    chat_id = str(query.message_event.sender.id)

                # 委托给kontext_handler处理
                async for message in self.kontext_handler.handle_kontext_workflow(
                    user_text, user_images, user_id, chat_id, query
                ):
                    yield message
            else:
                kontext_error_msg = self.session_manager.generate_workflow_error_message(
                    "Kontext处理器未初始化，无法执行工作流", "initialization"
                )
                kontext_error_message = message_sender.create_text_message(kontext_error_msg)
                yield llm_entities.Message(
                    role=kontext_error_message['role'],
                    content=kontext_error_message['content']
                )

        except Exception as e:
            self.ap.logger.error(f"执行kontext工作流失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            error_msg = self.session_manager.generate_workflow_error_message(
                f"执行Kontext工作流时出错: {str(e)}", "general"
            )
            error_message = message_sender.create_text_message(error_msg)
            yield llm_entities.Message(
                role=error_message['role'],
                content=error_message['content']
            )
            
            # 🔥 关键修复：Kontext工作流异常时也清理会话
            try:
                user_id = self._get_user_id(query)
                chat_id = self._get_chat_id(query)
                self.ap.logger.info(f"🎯 Kontext工作流执行异常，清理会话 - user_id: {user_id}, chat_id: {chat_id}")
                self._cleanup_session_completely(user_id, chat_id, "Kontext异常")
            except Exception as cleanup_error:
                self.ap.logger.error(f"❌ Kontext异常会话清理失败: {cleanup_error}")

    # 🔥 新增：执行kontext_api工作流的方法
    async def _execute_kontext_api_workflow(self, session, query: core_entities.Query) -> typing.AsyncGenerator[llm_entities.Message, None]:
        """执行kontext_api工作流"""
        try:
            # 确保处理器已经初始化
            if self.kontext_handler is None:
                self._initialize_handlers(query)

            if self.kontext_handler:
                # 提取必要的参数 - 添加安全的空检查
                user_text = ""
                if hasattr(query, 'message_event') and query.message_event and hasattr(query.message_event, 'message_chain') and query.message_event.message_chain:
                    user_text = query.message_event.message_chain.display if hasattr(query.message_event.message_chain, 'display') else ""
                
                user_images = self.kontext_handler.extract_user_images(query.message_event) if hasattr(query, 'message_event') and query.message_event else []
                
                # 安全获取user_id和chat_id
                user_id = "unknown"
                chat_id = "unknown"
                if hasattr(query, 'message_event') and query.message_event and hasattr(query.message_event, 'sender') and query.message_event.sender and hasattr(query.message_event.sender, 'id'):
                    user_id = str(query.message_event.sender.id)
                    chat_id = str(query.message_event.sender.id)

                # 委托给kontext_handler处理API工作流
                async for message in self.kontext_handler.handle_kontext_api_workflow(user_text, user_images, user_id, chat_id, query):
                    yield message
            else:
                kontext_api_error_msg = self.session_manager.generate_workflow_error_message(
                    "Kontext处理器未初始化，无法执行API工作流", "initialization"
                )
                kontext_api_error_message = message_sender.create_text_message(kontext_api_error_msg)
                yield llm_entities.Message(
                    role=kontext_api_error_message['role'],
                    content=kontext_api_error_message['content']
                )

        except Exception as e:
            self.ap.logger.error(f"执行kontext_api工作流失败: {e}")
            import traceback
            self.ap.logger.error(f"错误详情: {traceback.format_exc()}")
            error_msg = self.session_manager.generate_workflow_error_message(
                f"执行Kontext API工作流时出错: {str(e)}", "general"
            )
            error_message = message_sender.create_text_message(error_msg)
            yield llm_entities.Message(
                role=error_message['role'],
                content=error_message['content']
            )
            
            # 🔥 关键修复：Kontext API工作流异常时也清理会话
            try:
                user_id = self._get_user_id(query)
                chat_id = self._get_chat_id(query)
                self.ap.logger.info(f"🎯 Kontext API工作流执行异常，清理会话 - user_id: {user_id}, chat_id: {chat_id}")
                self._cleanup_session_completely(user_id, chat_id, "Kontext API异常")
            except Exception as cleanup_error:
                self.ap.logger.error(f"❌ Kontext API异常会话清理失败: {cleanup_error}")

    def _generate_workflow_options(self, session_images: List, user_text: str) -> str:
        """生成工作流选项列表"""
        # 注意：session_images和user_text参数保留以维持接口兼容性
        workflow_files = []
        try:
            import os
            workflow_dir = "workflows"
            if os.path.exists(workflow_dir):
                for file in os.listdir(workflow_dir):
                    if file.endswith('.json'):
                        workflow_files.append(file)
        except Exception as e:
            self.ap.logger.error(f"获取工作流列表失败: {e}")
        
        if not workflow_files:
            return "暂无可用的工作流文件"
        
        options = "📋 **可用工作流**：\n\n"
        for i, workflow_file in enumerate(workflow_files[:8], 1):  # 最多显示8个
            workflow_name = workflow_file.replace('.json', '').replace('_', ' ').title()
            options += f"**{i}**. {workflow_name} `({workflow_file})`\n"
        
        options += f"\n💬 **使用方法**: 请回复对应的**数字编号**（如：1）"
        return options

    def _generate_smart_routing_response(self, routing_result, session) -> str:
        """根据智能路由结果生成响应内容"""
        try:
            workflow_type = routing_result.workflow_type
            confidence = routing_result.confidence.value
            reasoning = routing_result.reasoning
            
            # 基础响应
            if workflow_type == WorkflowType.AIGEN:
                response = f"🎨 **Aigen工作流已启动**\n"
            elif workflow_type == WorkflowType.KONTEXT:
                response = f"✏️ **Kontext工作流已启动**\n"
            elif workflow_type == WorkflowType.KONTEXT_API:
                response = f"☁️ **远程API工作流已启动**\n"
            else:
                response = f"🤖 **{workflow_type.value}工作流已启动**\n"
            
            # 添加智能分析信息
            response += f"🧠 **智能分析**: {reasoning}\n"
            response += f"📊 **置信度**: {confidence}\n"
            response += f"🖼️ **图片**: {len(session.images)} 张\n"
            
            # 根据置信度提供不同的提示
            if confidence in ['high', 'medium']:
                response += f"🚀 **可发送指令**: 开始 | go | 取消"
            else:
                response += f"⚠️ **建议**: 路由置信度较低，建议确认后再发送：开始 | go | 取消"
            
            return response
            
        except Exception as e:
            self.ap.logger.error(f"生成智能路由响应失败: {e}")
            return f"🎨 **工作流已启动**\n🚀 可发送指令： 开始 | go | 取消"

    async def _check_kontext_session_compatibility(self, user_id: str, chat_id: str, current_session, query):
        """检查Kontext会话兼容性 - 继承自smart_hybrid_agent"""
        try:
            from ...workers.kontext.kontext_session_manager import KontextSessionManager
            
            session_manager = KontextSessionManager()
            active_kontext_session = session_manager.get_user_session(user_id, chat_id)
            
            if active_kontext_session:
                self.ap.logger.info(f"🔄 检测到活跃的Kontext会话，保持兼容性")
                # 如果有活跃的Kontext会话但当前会话不是Kontext类型，进行同步
                if current_session.workflow_type not in [WorkflowType.KONTEXT, WorkflowType.KONTEXT_API]:
                    self.ap.logger.warning(f"⚠️ 会话类型不匹配，Kontext会话存在但当前会话为: {current_session.workflow_type.value}")
                    
        except Exception as e:
            self.ap.logger.error(f"检查Kontext会话兼容性时出错: {e}")
            # 错误时不中断主流程

    def _cleanup_session_completely(self, user_id: str, chat_id: str, reason: str = "完成"):
        """
        完整清理会话，确保所有数据都被清除

        Args:
            user_id: 用户ID
            chat_id: 聊天ID
            reason: 清理原因
        """
        try:
            self.ap.logger.info(f"🧹 开始完整清理会话 - 用户: {user_id}, 聊天: {chat_id}, 原因: {reason}")

            # 1. 获取会话信息（用于日志）
            session = self.session_manager.get_session(user_id, chat_id)
            if session:
                image_count = len(session.images)
                message_count = len(session.messages)
                self.ap.logger.info(f"📊 会话数据统计 - 图片: {image_count}, 消息: {message_count}")

                # 2. 清理图片数据
                if session.images:
                    session.images.clear()
                    self.ap.logger.info(f"🖼️ 已清理 {image_count} 张图片")

                # 3. 清理消息历史
                if session.messages:
                    session.messages.clear()
                    self.ap.logger.info(f"💬 已清理 {message_count} 条消息")

                # 4. 清理其他数据
                session.prompt = ""
                session.quoted_text = ""
                session.quoted_images.clear()
                session.parameters.clear()
                session.routing_result = None
                session.forced_workflow_file = None

                self.ap.logger.info("🗑️ 已清理所有会话数据")

            # 5. 删除会话
            success = self.session_manager.delete_session(user_id, chat_id)
            if success:
                self.ap.logger.info(f"✅ 会话清理完成 - 原因: {reason}")
            else:
                self.ap.logger.warning(f"⚠️ 会话删除失败，但数据已清理 - 原因: {reason}")

            # 6. 同时清理Kontext会话（如果存在）
            try:
                from ...workers.kontext.kontext_session_manager import KontextSessionManager
                kontext_manager = KontextSessionManager()
                kontext_manager.remove_session(user_id, chat_id)
                self.ap.logger.info("🔄 已同步清理Kontext会话")
            except Exception as kontext_error:
                self.ap.logger.warning(f"⚠️ Kontext会话清理失败: {kontext_error}")

        except Exception as e:
            self.ap.logger.error(f"❌ 完整会话清理失败: {e}")
            # 最后的保险措施：强制删除
            try:
                self.session_manager.delete_session(user_id, chat_id)
                self.ap.logger.info("🚨 强制删除会话成功")
            except Exception as force_error:
                self.ap.logger.error(f"❌ 强制删除会话也失败: {force_error}")

