#!/usr/bin/env python3
"""
修复--again功能的脚本

主要功能：
1. 检测当前运行环境
2. 查找现有的工作流数据
3. 修复路径配置问题
4. 提供数据迁移选项
"""

import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
import hashlib

def detect_environment():
    """检测当前运行环境"""
    print("🔍 检测运行环境")
    print("=" * 50)
    
    env_info = {
        'os': os.name,
        'platform': sys.platform,
        'cwd': os.getcwd(),
        'is_docker': os.path.exists('/app'),
        'python_path': sys.executable
    }
    
    print(f"🖥️ 操作系统: {env_info['os']} ({env_info['platform']})")
    print(f"📁 当前目录: {env_info['cwd']}")
    print(f"🐳 Docker环境: {env_info['is_docker']}")
    print(f"🐍 Python路径: {env_info['python_path']}")
    
    return env_info

def find_workflow_data():
    """查找现有的工作流数据"""
    print("\n🔍 查找现有工作流数据")
    print("=" * 50)
    
    # 可能的数据位置
    possible_locations = [
        "temp",
        "/app/temp",
        "/home/<USER>/Workspace/langbot/temp",
        "D:/langbot/temp",
        "../temp",
        "../../temp"
    ]
    
    found_data = []
    
    for location in possible_locations:
        if os.path.exists(location):
            try:
                files = os.listdir(location)
                workflow_files = [f for f in files if f.startswith("last_workflow_")]
                history_files = [f for f in files if f.startswith("workflow_history_")]
                
                if workflow_files or history_files:
                    found_data.append({
                        'location': location,
                        'workflow_files': workflow_files,
                        'history_files': history_files,
                        'total_files': len(workflow_files) + len(history_files)
                    })
                    
                    print(f"✅ 找到数据: {location}")
                    print(f"   📄 工作流文件: {len(workflow_files)}")
                    print(f"   📚 历史文件: {len(history_files)}")
                    
                    # 显示一些示例文件
                    if workflow_files:
                        print(f"   📋 示例: {workflow_files[0]}")
                        
            except Exception as e:
                print(f"❌ 检查 {location} 失败: {e}")
        else:
            print(f"❌ 不存在: {location}")
    
    return found_data

def analyze_workflow_files(data_locations):
    """分析工作流文件内容"""
    print("\n🔍 分析工作流文件")
    print("=" * 50)
    
    for data_loc in data_locations:
        location = data_loc['location']
        print(f"\n📁 位置: {location}")
        
        for filename in data_loc['workflow_files'][:3]:  # 只分析前3个文件
            file_path = Path(location) / filename
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                user_id = data.get('user_id', 'N/A')
                workflow_type = data.get('workflow_type', 'N/A')
                timestamp = data.get('timestamp', 0)
                user_prompt = data.get('user_prompt', 'N/A')
                
                if timestamp:
                    file_time = datetime.fromtimestamp(timestamp)
                    time_diff = datetime.now() - file_time
                    is_expired = time_diff.total_seconds() > 24 * 3600
                    
                    print(f"   📄 {filename}")
                    print(f"      👤 用户: {user_id}")
                    print(f"      🔄 类型: {workflow_type}")
                    print(f"      ⏰ 时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"      ⌛ 过期: {'是' if is_expired else '否'}")
                    print(f"      💬 提示: {user_prompt[:30]}...")
                    
            except Exception as e:
                print(f"   ❌ 读取 {filename} 失败: {e}")

def test_current_config():
    """测试当前配置"""
    print("\n🔍 测试当前配置")
    print("=" * 50)
    
    try:
        # 添加当前目录到Python路径
        current_dir = os.getcwd()
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        from pkg.workers.flux.workflow_persistence_manager import WorkflowPersistenceManager
        
        manager = WorkflowPersistenceManager()
        print(f"✅ 持久化管理器初始化成功")
        print(f"📁 存储目录: {manager.storage_dir.absolute()}")
        
        # 测试用户ID生成
        test_user_id = "wxid_dtkfja2kjsa322"
        user_file = manager._get_user_workflow_file(test_user_id)
        print(f"📄 期望文件路径: {user_file}")
        print(f"📂 文件存在: {user_file.exists()}")
        
        return manager
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None

def create_test_data(manager):
    """创建测试数据"""
    print("\n🔧 创建测试数据")
    print("=" * 50)
    
    if not manager:
        print("❌ 管理器未初始化，跳过测试数据创建")
        return
    
    try:
        # 创建测试工作流数据
        test_data = {
            'workflow_data': {'test': 'data'},
            'user_prompt': '测试提示词',
            'optimized_prompt': 'test prompt',
            'workflow_type': 'flux_default',
            'parameters': {'width': 1024, 'height': 1024},
            'lora_info': {'loras_used': []},
            'image_info': {},
            'user_id': 'test_user_123',
            'chat_id': 'test_chat'
        }
        
        success = manager.save_successful_workflow(**test_data)
        
        if success:
            print("✅ 测试数据创建成功")
            
            # 尝试加载测试数据
            snapshot = manager.load_last_successful_workflow('test_user_123', 'test_chat')
            if snapshot:
                print("✅ 测试数据加载成功")
                print(f"   💬 提示词: {snapshot.user_prompt}")
                print(f"   🔄 类型: {snapshot.workflow_type}")
            else:
                print("❌ 测试数据加载失败")
        else:
            print("❌ 测试数据创建失败")
            
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")

def provide_solutions(env_info, found_data):
    """提供解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    if not found_data:
        print("🔧 方案1: 重新生成数据")
        print("   - 执行一次正常的图片生成命令")
        print("   - 确保工作流执行成功")
        print("   - 然后尝试使用 --again 功能")
        
    else:
        print("🔧 方案1: 数据迁移")
        print("   - 发现了现有的工作流数据")
        print("   - 可以将数据复制到当前环境的temp目录")
        
        # 找到最佳的数据源
        best_source = max(found_data, key=lambda x: x['total_files'])
        current_temp = Path("temp")
        
        print(f"   - 源目录: {best_source['location']}")
        print(f"   - 目标目录: {current_temp.absolute()}")
        
        print("\n🔧 方案2: 环境统一")
        print("   - 确保langbot在正确的环境中运行")
        print("   - 检查Docker配置和路径映射")
        
    print("\n🔧 方案3: 配置检查")
    print("   - 检查用户ID提取逻辑")
    print("   - 确认文件命名规则一致")
    print("   - 验证路径配置正确")

def main():
    """主函数"""
    print("🔧 --again功能修复脚本")
    print("=" * 60)
    
    # 1. 检测环境
    env_info = detect_environment()
    
    # 2. 查找数据
    found_data = find_workflow_data()
    
    # 3. 分析文件
    if found_data:
        analyze_workflow_files(found_data)
    
    # 4. 测试当前配置
    manager = test_current_config()
    
    # 5. 创建测试数据
    create_test_data(manager)
    
    # 6. 提供解决方案
    provide_solutions(env_info, found_data)
    
    print("\n" + "=" * 60)
    print("🎯 修复脚本完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
