#!/usr/bin/env python3
"""
调试--again功能问题的脚本

主要检查：
1. temp目录中的文件状态
2. 用户ID命名规则
3. 文件内容和过期时间
4. 数据加载逻辑
"""

import os
import json
import time
from pathlib import Path
from datetime import datetime, timedelta
import hashlib

def check_temp_directory():
    """检查temp目录状态"""
    print("=" * 60)
    print("🔍 检查temp目录状态")
    print("=" * 60)
    
    temp_dir = Path("temp")
    if not temp_dir.exists():
        print("❌ temp目录不存在")
        return
    
    print(f"📁 temp目录路径: {temp_dir.absolute()}")
    
    # 列出所有文件
    files = list(temp_dir.glob("*"))
    print(f"📄 总文件数: {len(files)}")
    
    # 分类文件
    workflow_files = []
    history_files = []
    other_files = []
    
    for file in files:
        if file.is_file():
            name = file.name
            if name.startswith("last_workflow_"):
                workflow_files.append(file)
            elif name.startswith("workflow_history_"):
                history_files.append(file)
            else:
                other_files.append(file)
    
    print(f"🔄 工作流文件: {len(workflow_files)}")
    for f in workflow_files:
        print(f"  - {f.name} ({f.stat().st_size} bytes)")
    
    print(f"📚 历史文件: {len(history_files)}")
    for f in history_files:
        print(f"  - {f.name} ({f.stat().st_size} bytes)")
    
    print(f"📋 其他文件: {len(other_files)}")
    for f in other_files:
        print(f"  - {f.name} ({f.stat().st_size} bytes)")

def analyze_user_id_pattern():
    """分析用户ID命名模式"""
    print("\n" + "=" * 60)
    print("🔍 分析用户ID命名模式")
    print("=" * 60)
    
    # 模拟用户ID
    test_user_ids = [
        "wxid_dtkfja2kjsa322",
        "person_wxid_dtkfja2kjsa322", 
        "unknown_user",
        "test_user_123"
    ]
    
    for user_id in test_user_ids:
        print(f"\n👤 用户ID: {user_id}")
        
        # 模拟文件名生成逻辑
        user_hash = hashlib.md5(user_id.encode('utf-8')).hexdigest()[:8]
        safe_chars = "".join(c for c in user_id if c.isalnum() or c in "._-")[:20]
        safe_filename = f"{safe_chars}_{user_hash}" if safe_chars else user_hash
        
        workflow_file = f"last_workflow_{safe_filename}.json"
        history_file = f"workflow_history_{safe_filename}.json"
        
        print(f"  📄 工作流文件: {workflow_file}")
        print(f"  📚 历史文件: {history_file}")
        
        # 检查文件是否存在
        temp_dir = Path("temp")
        workflow_path = temp_dir / workflow_file
        history_path = temp_dir / history_file
        
        print(f"  ✅ 工作流文件存在: {workflow_path.exists()}")
        print(f"  ✅ 历史文件存在: {history_path.exists()}")

def check_file_contents():
    """检查现有文件内容"""
    print("\n" + "=" * 60)
    print("🔍 检查现有文件内容")
    print("=" * 60)
    
    temp_dir = Path("temp")
    
    # 检查所有工作流文件
    workflow_files = list(temp_dir.glob("last_workflow_*.json"))
    
    for file in workflow_files:
        print(f"\n📄 文件: {file.name}")
        try:
            with open(file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查关键字段
            user_id = data.get('user_id', 'N/A')
            workflow_type = data.get('workflow_type', 'N/A')
            timestamp = data.get('timestamp', 0)
            user_prompt = data.get('user_prompt', 'N/A')
            
            # 计算时间差
            if timestamp:
                file_time = datetime.fromtimestamp(timestamp)
                time_diff = datetime.now() - file_time
                is_expired = time_diff > timedelta(hours=24)
                
                print(f"  👤 用户ID: {user_id}")
                print(f"  🔄 工作流类型: {workflow_type}")
                print(f"  ⏰ 创建时间: {file_time.strftime('%Y-%m-%d %H:%M:%S')}")
                print(f"  ⌛ 时间差: {time_diff}")
                print(f"  ❌ 是否过期: {is_expired}")
                print(f"  💬 提示词: {user_prompt[:50]}...")
            else:
                print(f"  ❌ 无效的时间戳")
                
        except Exception as e:
            print(f"  ❌ 读取文件失败: {e}")

def test_persistence_manager():
    """测试持久化管理器"""
    print("\n" + "=" * 60)
    print("🔍 测试持久化管理器")
    print("=" * 60)
    
    try:
        from pkg.workers.flux.workflow_persistence_manager import WorkflowPersistenceManager
        
        manager = WorkflowPersistenceManager()
        print("✅ 持久化管理器初始化成功")
        
        # 测试用户ID
        test_user_ids = [
            "wxid_dtkfja2kjsa322",
            "person_wxid_dtkfja2kjsa322"
        ]
        
        for user_id in test_user_ids:
            print(f"\n👤 测试用户: {user_id}")
            
            # 尝试加载数据
            snapshot = manager.load_last_successful_workflow(user_id, "")
            
            if snapshot:
                print(f"  ✅ 找到数据: {snapshot.workflow_type}")
                print(f"  💬 提示词: {snapshot.user_prompt[:50]}...")
                print(f"  ⏰ 时间: {datetime.fromtimestamp(snapshot.timestamp)}")
            else:
                print(f"  ❌ 未找到数据")
                
                # 检查对应的文件是否存在
                user_file = manager._get_user_workflow_file(user_id)
                print(f"  📄 期望文件: {user_file}")
                print(f"  📁 文件存在: {user_file.exists()}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    print("🔧 --again功能调试脚本")
    print("=" * 60)
    
    check_temp_directory()
    analyze_user_id_pattern()
    check_file_contents()
    test_persistence_manager()
    
    print("\n" + "=" * 60)
    print("🎯 调试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
